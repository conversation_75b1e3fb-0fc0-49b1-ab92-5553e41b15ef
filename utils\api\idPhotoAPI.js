/**
 * 证件照API工具类 (新版本)
 * 基于新的服务端API文档封装证件照相关接口
 */

const apiConfig = require('../../config/apiConfig');

/**
 * 获取用户token
 */
function getUserToken() {
  const app = getApp();
  return app.globalData.userToken || '';
}

/**
 * 健康检查
 * @returns {Promise<Object>} API响应结果
 */
async function healthCheck() {
  try {
    console.log('开始健康检查...');

    return new Promise((resolve, reject) => {
      wx.request({
        url: apiConfig.idPhotoBaseUrl + apiConfig.idPhotoHealthUrl,
        method: 'GET',
        timeout: 5000,
        success: (res) => {
          console.log('健康检查响应:', res);

          if (res.statusCode === 200 && res.data) {
            resolve({
              success: true,
              data: res.data
            });
          } else {
            reject(new Error(`健康检查失败，状态码: ${res.statusCode}`));
          }
        },
        fail: (err) => {
          console.error('健康检查请求失败:', err);
          reject(new Error('健康检查请求失败'));
        }
      });
    });

  } catch (error) {
    console.error('健康检查失败:', error);
    return {
      success: false,
      message: error.message || '健康检查失败'
    };
  }
}

/**
 * 获取支持的尺寸列表
 * @returns {Promise<Object>} API响应结果
 */
async function getSizes() {
  try {
    console.log('开始获取尺寸列表...');

    return new Promise((resolve, reject) => {
      wx.request({
        url: apiConfig.idPhotoBaseUrl + apiConfig.idPhotoSizesUrl,
        method: 'GET',
        timeout: 10000,
        success: (res) => {
          console.log('获取尺寸列表响应:', res);

          if (res.statusCode === 200 && res.data && res.data.success) {
            resolve({
              success: true,
              data: res.data.data
            });
          } else {
            reject(new Error(res.data?.message || '获取尺寸列表失败'));
          }
        },
        fail: (err) => {
          console.error('获取尺寸列表请求失败:', err);
          let errorMessage = '网络请求失败';

          if (err.errMsg) {
            if (err.errMsg.includes('timeout')) {
              errorMessage = '请求超时，请重试';
            } else if (err.errMsg.includes('fail')) {
              errorMessage = '网络连接失败';
            }
          }

          reject(new Error(errorMessage));
        }
      });
    });

  } catch (error) {
    console.error('获取尺寸列表失败:', error);
    return {
      success: false,
      message: error.message || '获取尺寸列表失败'
    };
  }
}

/**
 * 获取支持的颜色列表
 * @returns {Promise<Object>} API响应结果
 */
async function getColors() {
  try {
    console.log('开始获取颜色列表...');

    return new Promise((resolve, reject) => {
      wx.request({
        url: apiConfig.idPhotoBaseUrl + apiConfig.idPhotoColorsUrl,
        method: 'GET',
        timeout: 10000,
        success: (res) => {
          console.log('获取颜色列表响应:', res);

          if (res.statusCode === 200 && res.data && res.data.success) {
            resolve({
              success: true,
              data: res.data.data
            });
          } else {
            reject(new Error(res.data?.message || '获取颜色列表失败'));
          }
        },
        fail: (err) => {
          console.error('获取颜色列表请求失败:', err);
          let errorMessage = '网络请求失败';

          if (err.errMsg) {
            if (err.errMsg.includes('timeout')) {
              errorMessage = '请求超时，请重试';
            } else if (err.errMsg.includes('fail')) {
              errorMessage = '网络连接失败';
            }
          }

          reject(new Error(errorMessage));
        }
      });
    });

  } catch (error) {
    console.error('获取颜色列表失败:', error);
    return {
      success: false,
      message: error.message || '获取颜色列表失败'
    };
  }
}

/**
 * 生成证件照 (新版本API)
 * @param {Object} params 参数对象
 * @param {string} params.imagePath 图片文件路径
 * @param {string} params.size 证件照尺寸，如：one_inch、two_inch等
 * @param {string} params.color 背景颜色，如：white、blue、red、transparent等
 * @returns {Promise<Object>} API响应结果
 */
async function generateIDPhoto(params) {
  try {
    console.log('开始生成证件照，参数:', params);

    const userToken = getUserToken();
    if (!userToken) {
      throw new Error('用户未登录，请先登录');
    }

    console.log('发送证件照生成请求...');

    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: apiConfig.idPhotoBaseUrl + apiConfig.idPhotoGenerateUrl,
        filePath: params.imagePath,
        name: 'image',
        formData: {
          size: params.size || 'one_inch',
          color: params.color || 'white'
        },
        header: {
          'Authorization': 'Bearer ' + userToken
        },
        timeout: 30000, // 30秒超时
        success: (res) => {
          console.log('证件照生成API响应:', res);

          try {
            const data = JSON.parse(res.data);

            if (res.statusCode === 200 && data.success) {
              resolve({
                success: true,
                data: {
                  imageBase64: 'data:image/jpeg;base64,' + data.data.image_base64,
                  hdImageBase64: data.data.hd_image_base64 ? 'data:image/jpeg;base64,' + data.data.hd_image_base64 : null,
                  transparentBase64: data.data.transparent_base64 ? 'data:image/png;base64,' + data.data.transparent_base64 : null,
                  size: data.data.size,
                  sizeName: data.data.size_name,
                  color: data.data.color,
                  colorName: data.data.color_name,
                  dimensions: data.data.dimensions
                }
              });
            } else {
              reject(new Error(data.message || '生成证件照失败'));
            }
          } catch (parseError) {
            console.error('解析响应数据失败:', parseError);
            reject(new Error('服务器响应格式错误'));
          }
        },
        fail: (err) => {
          console.error('证件照生成请求失败:', err);
          let errorMessage = '网络请求失败';

          if (err.errMsg) {
            if (err.errMsg.includes('timeout')) {
              errorMessage = '请求超时，请重试';
            } else if (err.errMsg.includes('fail')) {
              errorMessage = '网络连接失败';
            }
          }

          reject(new Error(errorMessage));
        }
      });
    });

  } catch (error) {
    console.error('生成证件照失败:', error);
    return {
      success: false,
      message: error.message || '生成证件照失败'
    };
  }
}



module.exports = {
  healthCheck,
  getSizes,
  getColors,
  generateIDPhoto
};

/**
 * 证件照API工具类
 * 基于服务端API文档封装证件照相关接口
 */

const apiConfig = require('../../config/apiConfig');

/**
 * 将图片文件转换为base64
 * @param {string} filePath 图片文件路径
 * @returns {Promise<string>} base64编码的图片数据
 */
function imageToBase64(filePath) {
  return new Promise((resolve, reject) => {
    wx.getFileSystemManager().readFile({
      filePath: filePath,
      encoding: 'base64',
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    });
  });
}

/**
 * 生成证件照(底透明)
 * @param {Object} params 参数对象
 * @param {string} params.imagePath 图片文件路径
 * @param {number} params.width 标准证件照宽度
 * @param {number} params.height 标准证件照高度
 * @param {string} params.humanMattingModel 人像分割模型，默认为modnet_photographic_portrait_matting
 * @param {string} params.faceDetectModel 人脸检测模型，默认为mtcnn
 * @param {boolean} params.hd 是否生成高清证件照，默认为true
 * @param {number} params.dpi 图像分辨率，默认为300
 * @param {boolean} params.faceAlignment 是否进行人脸对齐，默认为true
 * @returns {Promise<Object>} API响应结果
 */
async function generateIDPhoto(params) {
  try {
    console.log('开始生成证件照，参数:', params);

    // 将图片转换为base64
    const imageBase64 = await imageToBase64(params.imagePath);
    console.log('图片转换为base64成功，长度:', imageBase64.length);

    // 构建请求数据
    const requestData = {
      input_image_base64: imageBase64,
      width: params.width || 295,
      height: params.height || 413,
      human_matting_model: params.humanMattingModel || 'modnet_photographic_portrait_matting',
      face_detect_model: params.faceDetectModel || 'mtcnn',
      hd: params.hd !== false, // 默认为true
      dpi: params.dpi || 300,
      face_alignment: params.faceAlignment !== false, // 默认为true
      head_measure_ratio: 0.2,
      head_height_ratio: 0.45,
      top_distance_max: 0.12,
      top_distance_min: 0.1,
      brightness_strength: 0,
      contrast_strength: 0,
      sharpen_strength: 0,
      saturation_strength: 0
    };

    console.log('发送证件照生成请求...');

    return new Promise((resolve, reject) => {
      wx.request({
        url: apiConfig.idPhotoBaseUrl + apiConfig.idPhotoGenerateUrl,
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: requestData,
        timeout: 30000, // 30秒超时
        success: (res) => {
          console.log('证件照生成API响应:', res);
          
          if (res.statusCode === 200 && res.data) {
            if (res.data.status) {
              resolve({
                success: true,
                data: {
                  standardImage: 'data:image/png;base64,' + res.data.image_base64_standard,
                  hdImage: res.data.image_base64_hd ? 'data:image/png;base64,' + res.data.image_base64_hd : null
                }
              });
            } else {
              reject(new Error('服务器处理失败'));
            }
          } else {
            reject(new Error(`请求失败，状态码: ${res.statusCode}`));
          }
        },
        fail: (err) => {
          console.error('证件照生成请求失败:', err);
          let errorMessage = '网络请求失败';
          
          if (err.errMsg) {
            if (err.errMsg.includes('timeout')) {
              errorMessage = '请求超时，请重试';
            } else if (err.errMsg.includes('fail')) {
              errorMessage = '网络连接失败';
            }
          }
          
          reject(new Error(errorMessage));
        }
      });
    });

  } catch (error) {
    console.error('生成证件照失败:', error);
    return {
      success: false,
      message: error.message || '生成证件照失败'
    };
  }
}

/**
 * 添加背景色
 * @param {Object} params 参数对象
 * @param {string} params.imageBase64 RGBA四通道图像的base64编码
 * @param {string} params.color 背景色HEX值，不包含#号
 * @param {number} params.kb 输出照片的KB值，可选
 * @param {number} params.render 渲染模式，默认为0（纯色）
 * @param {number} params.dpi 图像分辨率，默认为300
 * @returns {Promise<Object>} API响应结果
 */
async function addBackground(params) {
  try {
    console.log('开始添加背景色，参数:', params);

    // 构建请求数据
    const requestData = {
      input_image_base64: params.imageBase64.replace(/^data:image\/\w+;base64,/, ''),
      color: params.color || 'ffffff',
      render: params.render || 0,
      dpi: params.dpi || 300
    };

    if (params.kb) {
      requestData.kb = params.kb;
    }

    console.log('发送添加背景色请求...');

    return new Promise((resolve, reject) => {
      wx.request({
        url: apiConfig.idPhotoBaseUrl + apiConfig.idPhotoAddBackgroundUrl,
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: requestData,
        timeout: 15000, // 15秒超时
        success: (res) => {
          console.log('添加背景色API响应:', res);
          
          if (res.statusCode === 200 && res.data) {
            if (res.data.status) {
              resolve({
                success: true,
                data: {
                  imageBase64: 'data:image/jpeg;base64,' + res.data.image_base64
                }
              });
            } else {
              reject(new Error('服务器处理失败'));
            }
          } else {
            reject(new Error(`请求失败，状态码: ${res.statusCode}`));
          }
        },
        fail: (err) => {
          console.error('添加背景色请求失败:', err);
          let errorMessage = '网络请求失败';
          
          if (err.errMsg) {
            if (err.errMsg.includes('timeout')) {
              errorMessage = '请求超时，请重试';
            } else if (err.errMsg.includes('fail')) {
              errorMessage = '网络连接失败';
            }
          }
          
          reject(new Error(errorMessage));
        }
      });
    });

  } catch (error) {
    console.error('添加背景色失败:', error);
    return {
      success: false,
      message: error.message || '添加背景色失败'
    };
  }
}

/**
 * 证件照裁切
 * @param {Object} params 参数对象
 * @param {string} params.imageBase64 RGBA四通道图像的base64编码
 * @param {number} params.width 标准证件照宽度
 * @param {number} params.height 标准证件照高度
 * @param {string} params.faceDetectModel 人脸检测模型，默认为mtcnn
 * @param {boolean} params.hd 是否生成高清证件照，默认为true
 * @param {number} params.dpi 图像分辨率，默认为300
 * @returns {Promise<Object>} API响应结果
 */
async function cropIDPhoto(params) {
  try {
    console.log('开始证件照裁切，参数:', params);

    // 构建请求数据
    const requestData = {
      input_image_base64: params.imageBase64.replace(/^data:image\/\w+;base64,/, ''),
      width: params.width || 295,
      height: params.height || 413,
      face_detect_model: params.faceDetectModel || 'mtcnn',
      hd: params.hd !== false, // 默认为true
      dpi: params.dpi || 300,
      head_measure_ratio: 0.2,
      head_height_ratio: 0.45,
      top_distance_max: 0.12,
      top_distance_min: 0.1
    };

    console.log('发送证件照裁切请求...');

    return new Promise((resolve, reject) => {
      wx.request({
        url: apiConfig.idPhotoBaseUrl + apiConfig.idPhotoCropUrl,
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: requestData,
        timeout: 20000, // 20秒超时
        success: (res) => {
          console.log('证件照裁切API响应:', res);
          
          if (res.statusCode === 200 && res.data) {
            if (res.data.status) {
              resolve({
                success: true,
                data: {
                  standardImage: 'data:image/png;base64,' + res.data.image_base64,
                  hdImage: res.data.image_base64_hd ? 'data:image/png;base64,' + res.data.image_base64_hd : null
                }
              });
            } else {
              reject(new Error('服务器处理失败'));
            }
          } else {
            reject(new Error(`请求失败，状态码: ${res.statusCode}`));
          }
        },
        fail: (err) => {
          console.error('证件照裁切请求失败:', err);
          let errorMessage = '网络请求失败';
          
          if (err.errMsg) {
            if (err.errMsg.includes('timeout')) {
              errorMessage = '请求超时，请重试';
            } else if (err.errMsg.includes('fail')) {
              errorMessage = '网络连接失败';
            }
          }
          
          reject(new Error(errorMessage));
        }
      });
    });

  } catch (error) {
    console.error('证件照裁切失败:', error);
    return {
      success: false,
      message: error.message || '证件照裁切失败'
    };
  }
}

module.exports = {
  generateIDPhoto,
  addBackground,
  cropIDPhoto
};

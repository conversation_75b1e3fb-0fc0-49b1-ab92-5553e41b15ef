{"pages": ["pages/index/index", "pages/makeResume/makeResume", "pages/makeResume/moduleManage/moduleManage", "pages/makeResume/basicInfo/basicInfo", "pages/makeResume/jobIntention/jobIntention", "pages/makeResume/education/education/education", "pages/makeResume/education/educationEdit/educationEdit", "pages/makeResume/school/school/school", "pages/makeResume/school/schoolEdit/schoolEdit", "pages/makeResume/internship/internshipExperience/internshipExperience", "pages/makeResume/internship/internshipEdit/internshipEdit", "pages/makeResume/work/work/work", "pages/makeResume/work/workEdit/workEdit", "pages/makeResume/project/project/project", "pages/makeResume/project/projectEdit/projectEdit", "pages/makeResume/skills/skills", "pages/makeResume/awards/awards", "pages/makeResume/interests/interests", "pages/makeResume/evaluation/evaluation", "pages/makeResume/custom/custom1/custom1", "pages/makeResume/custom/custom2/custom2", "pages/makeResume/custom/custom3/custom3", "pages/makeCreateResume/makeCreateResume", "pages/resumeStyle/resumeStyle", "pages/idPhoto/idPhoto", "pages/idPhoto/result/result", "pages/freeResume/index", "pages/feedback/feedback"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#4B8BF5", "navigationBarTitleText": "个人简历模板Offer必来", "navigationBarTextStyle": "white", "backgroundColor": "#4B8BF5"}, "style": "v2", "componentFramework": "glass-easel", "lazyCodeLoading": "requiredComponents"}
// pages/idPhoto/idPhoto.js
Page({
  data: {
    selectedSize: '', // 选中的尺寸
    selectedImage: '', // 选中的图片路径
    processing: false, // 是否正在处理
    sizeOptions: [
      {
        key: 'one_inch',
        name: '一寸',
        width: 295,
        height: 413,
        mmWidth: 25,
        mmHeight: 35
      },
      {
        key: 'two_inch',
        name: '二寸',
        width: 413,
        height: 579,
        mmWidth: 35,
        mmHeight: 49
      },
      {
        key: 'big_one_inch',
        name: '大一寸',
        width: 390,
        height: 567,
        mmWidth: 33,
        mmHeight: 48
      },
      {
        key: 'small_one_inch',
        name: '小一寸',
        width: 260,
        height: 378,
        mmWidth: 22,
        mmHeight: 32
      },
      {
        key: 'big_two_inch',
        name: '大二寸',
        width: 449,
        height: 661,
        mmWidth: 38,
        mmHeight: 56
      },
      {
        key: 'small_two_inch',
        name: '小二寸',
        width: 378,
        height: 531,
        mmWidth: 32,
        mmHeight: 45
      }
    ]
  },

  onLoad(options) {
    console.log('证件照制作页面加载');
  },

  /**
   * 选择尺寸
   */
  selectSize(e) {
    const size = e.currentTarget.dataset.size;
    console.log('选择尺寸:', size);

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    this.setData({
      selectedSize: size
    });
  },

  /**
   * 选择图片
   */
  chooseImage() {
    const that = this;

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success(res) {
        console.log('选择图片成功:', res);
        const tempFilePath = res.tempFiles[0].tempFilePath;
        that.setData({
          selectedImage: tempFilePath
        });

        // 选择成功后的提示
        wx.showToast({
          title: '照片选择成功',
          icon: 'success',
          duration: 1500
        });
      },
      fail(err) {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 开始处理证件照
   */
  startProcess() {
    if (!this.data.selectedSize || !this.data.selectedImage) {
      wx.showToast({
        title: '请选择尺寸和照片',
        icon: 'error'
      });
      return;
    }

    console.log('开始处理证件照');
    console.log('选中尺寸:', this.data.selectedSize);
    console.log('选中图片:', this.data.selectedImage);

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'medium'
    });

    // 显示加载提示
    wx.showLoading({
      title: '正在跳转...',
      mask: true
    });

    // 获取选中尺寸的详细信息
    const selectedSizeInfo = this.data.sizeOptions.find(item => item.key === this.data.selectedSize);

    // 跳转到处理页面，传递参数
    wx.navigateTo({
      url: `/pages/idPhoto/result/result?image=${encodeURIComponent(this.data.selectedImage)}&size=${this.data.selectedSize}&width=${selectedSizeInfo.width}&height=${selectedSizeInfo.height}`,
      success: () => {
        wx.hideLoading();
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  }
});

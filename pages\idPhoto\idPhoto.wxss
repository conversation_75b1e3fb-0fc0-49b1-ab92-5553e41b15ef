.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部标题 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 0;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

/* 尺寸选择区域 */
.size-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.size-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.size-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  border: 2rpx solid #e0e0e0;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.size-item.selected {
  border-color: #4B8BF5;
  background: linear-gradient(135deg, #4B8BF5 0%, #6BA3F7 100%);
  color: white;
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 25rpx rgba(75, 139, 245, 0.3);
}

.size-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.size-desc {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 4rpx;
}

.size-mm {
  font-size: 22rpx;
  opacity: 0.7;
}

/* 照片上传区域 */
.upload-section {
  margin-bottom: 40rpx;
}

.upload-area {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  position: relative;
}

.upload-placeholder {
  padding: 80rpx 40rpx;
  text-align: center;
  border: 2rpx dashed #ddd;
  margin: 20rpx;
  border-radius: 16rpx;
  background: #fafafa;
}

.upload-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.upload-text {
  font-size: 32rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.upload-hint {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.upload-preview {
  position: relative;
  height: 400rpx;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.upload-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.upload-preview:active .upload-mask {
  opacity: 1;
}

.change-text {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

/* 开始制作按钮 */
.action-section {
  margin-bottom: 40rpx;
}

.start-btn {
  width: 100%;
  height: 100rpx;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.start-btn.active {
  background: linear-gradient(135deg, #4B8BF5 0%, #6BA3F7 100%);
  color: white;
  box-shadow: 0 8rpx 25rpx rgba(75, 139, 245, 0.4);
}

.start-btn.disabled {
  background: #e0e0e0;
  color: #999;
}

.start-btn.active:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(75, 139, 245, 0.3);
}

/* 使用说明 */
.tips-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.tips-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tip-item {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

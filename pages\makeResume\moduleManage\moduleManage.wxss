.module-manage {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
  box-sizing: border-box;
}

.module-list {
  background: #fff;
  border-radius: 12rpx;
  padding: 0;
  margin-bottom: 40rpx;
  overflow: hidden;
  position: relative;
  min-height: 600rpx;
}

.module-container {
  width: 100%;
  padding: 0;
  position: relative;
}

.module-item {
  padding: 20rpx;
  background-color: #fff;
  box-sizing: border-box;
  margin-bottom: 1rpx;
  border-bottom: 1rpx solid #eee;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.2s ease;
}

.module-item:hover {
  background-color: #f8f9fa;
}

.module-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.module-controls {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.control-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #e0e0e0;
  transition: all 0.2s ease;
}

.control-btn:active {
  background-color: #4080ff;
  border-color: #4080ff;
  transform: scale(0.95);
}

.control-btn:active .icon {
  color: #fff;
}

.control-btn .icon {
  font-size: 32rpx;
  color: #666;
  font-weight: bold;
  transition: color 0.2s ease;
}

.up-btn:hover {
  background-color: #e8f4fd;
  border-color: #4080ff;
}

.down-btn:hover {
  background-color: #e8f4fd;
  border-color: #4080ff;
}

.module-name {
  font-size: 32rpx;
  color: #333;
  flex: 1;
  font-weight: 400;
}

.basic-info-item {
  background-color: #f8f9ff;
  border-left: 4rpx solid #4080ff;
}

.basic-info-item .module-name {
  color: #4080ff;
  font-weight: 500;
}

.basic-info-item .control-btn {
  opacity: 0.5;
  pointer-events: none;
}

.module-tip {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.tip-text {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  padding: 40rpx 0;
}

.save-btn {
  position: fixed;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: #4080ff;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 8rpx rgba(64,128,255,0.2);
}
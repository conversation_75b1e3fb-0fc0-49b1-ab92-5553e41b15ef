<view class="container">
  <!-- 顶部标题 -->
  <view class="header">
    <text class="title">证件照制作完成</text>
    <text class="subtitle">选择底色，保存到相册</text>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-section">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{loadingText}}</text>
  </view>

  <!-- 结果展示区域 -->
  <view wx:else class="result-section">
    <!-- 证件照预览 -->
    <view class="photo-preview">
      <view class="preview-container">
        <image 
          class="result-image" 
          src="{{currentImage}}" 
          mode="aspectFit"
          show-menu-by-longpress="{{true}}"
        ></image>
        <view class="size-info">
          <text>{{sizeInfo.name}} ({{sizeInfo.width}}×{{sizeInfo.height}})</text>
        </view>
      </view>
    </view>

    <!-- 底色选择 -->
    <view class="background-section">
      <text class="section-title">选择背景色</text>
      <view class="color-options">
        <view 
          class="color-item {{selectedBackground === item.key ? 'selected' : ''}}"
          wx:for="{{backgroundOptions}}"
          wx:key="key"
          bindtap="selectBackground"
          data-color="{{item.key}}"
        >
          <view class="color-preview" style="background-color: {{item.color}};"></view>
          <text class="color-name">{{item.name}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="save-btn" bindtap="saveToAlbum" disabled="{{saving}}">
        <text wx:if="{{!saving}}">保存到相册</text>
        <text wx:else>保存中...</text>
      </button>
      <button class="remake-btn" bindtap="remakePhoto">重新制作</button>
    </view>

    <!-- 高清版本提示 -->
    <view wx:if="{{hdImage}}" class="hd-section">
      <view class="hd-tip">
        <text class="hd-title">高清版本已生成</text>
        <text class="hd-desc">点击保存将同时保存标准版和高清版</text>
      </view>
    </view>
  </view>

  <!-- 错误提示 -->
  <view wx:if="{{error}}" class="error-section">
    <view class="error-content">
      <text class="error-title">处理失败</text>
      <text class="error-message">{{errorMessage}}</text>
      <button class="retry-btn" bindtap="retryProcess">重试</button>
      <button class="back-btn" bindtap="goBack">返回</button>
    </view>
  </view>
</view>
